local nani = "大世界区域一/支线/燕燕于飞"
local taskKey = "燕燕于飞"

--------------公共函数---------------------
---@type RpgMapApi
local rapi = require("RpgMapApi")
---@type RpgMapFlags
local flags = require("RpgMap/flags")
---@type NewbieFlags
local NBflags = require("Newbie/flags") 
local newbieApi = require("NewbieApi")
rapi.loadFlags(flags)


local function playNani(tag)
    return rapi.runAvgTag(nani,tag)
end

local function nextStep(nextTaskStep)
    rapi.nextTaskStep(taskKey, nextTaskStep)
end

local function isStep(stepKey)
    return rapi.getCurrentTaskStep(taskKey) == stepKey
end

local function isTaskGroupInProgress()
    local notInProgress = isStep("deactive") or isStep("finished")
    return not notInProgress
end

local function dayTime()
    return rapi.getDayNight()
end

local function refreshBigMap()
    rapi.refreshBigMap()
end

local timeSystem = {
    isDaytime = function(self)
        return rapi.getDayNight() == 1
    end,

    checkTime = function(self, expectedDaytime, wrongTimeMessage)
        local isDaytime = self:isDaytime()
        if isDaytime ~= expectedDaytime then
            playNani(wrongTimeMessage)
            return false
        end
        return true
    end
}   

--------------------------------------------------------@客服鼠鼠
--客服鼠鼠调用，燕燕于飞任务测试环境初始化

function beginTest()
    -- 清空内容
end
-----------------------------------区域一地图支线《燕燕于飞》第一段@taskId.0 active

function yyyf_001()
    playNani("燕燕于飞_001_任务开始")
end

function yyyf_002()
    playNani("燕燕于飞_002_寻找镖师")
end

function yyyf_003()
    playNani("燕燕于飞_003_交付药草")
end

function yyyf_004()
    playNani("燕燕于飞_004_护送商贾")
end

function yyyf_005()
    playNani("燕燕于飞_005_追查线索")
end

function yyyf_006()
    playNani("燕燕于飞_006_楚家庄")
end

function yyyf_007()
    playNani("燕燕于飞_007_酒楼相会")
end

function yyyf_008()
    playNani("燕燕于飞_008_送金燕桃回家")
end

function yyyf_009()
    playNani("燕燕于飞_009_任务完成")
end
--测试
function test()
    -- 清空内容
end

--AVG收集物品测试
function test2()
    -- 清空内容
end
--AVG收集物品测试2
function test3()
    -- 清空内容
end
function endTest()
    -- 清空内容
end
return {       
    beginTest = beginTest,
    yyyf_001 = yyyf_001,
    yyyf_002 = yyyf_002,
    yyyf_003 = yyyf_003,
    yyyf_004 = yyyf_004,
    yyyf_005 = yyyf_005,
    yyyf_006 = yyyf_006,
    yyyf_007 = yyyf_007,
    yyyf_008 = yyyf_008,
    yyyf_009 = yyyf_009,
    test = test,
    test2 = test2,
    test3 = test3,
    endTest = endTest,
}

